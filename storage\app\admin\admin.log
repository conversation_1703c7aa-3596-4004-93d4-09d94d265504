[2025-08-29 02:15:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Processed 1 expired requests  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-29 02:17:23] local.ERROR: {"query":{"statusType":"APPROVED"},"parameter":{"statusType":"APPROVED"},"error":"ParseError","message":"syntax error, unexpected token \",\", expecting identifier or variable or \"{\" or \"$\"","code":0}  
[2025-08-29 02:44:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:45:28] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:11] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:15] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:18] local.ERROR: {"query":{"statusType":"DELETED","orderby":"email:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"email:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:21] local.ERROR: {"query":{"statusType":"DELETED","orderby":"domain:asc","email":null},"parameter":{"statusType":"DELETED","orderby":"domain:asc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:26] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:32] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:39] local.ERROR: {"query":{"statusType":"ALL","email":"asd"},"parameter":{"statusType":"ALL","email":"asd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
